import React from 'react';
import JWTSection from './JWTSection';

interface JWTOutputProps {
  decodedHeader: string;
  decodedPayload: string;
  decodedSignature: string;
}

export default function JWTOutput({ decodedHeader, decodedPayload, decodedSignature }: JWTOutputProps) {
  return (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
    }}>
      <JWTSection title="Header" content={decodedHeader} copyText={decodedHeader} />
      <JWTSection title="Payload" content={decodedPayload} copyText={decodedPayload} />
      <JWTSection title="Signature" content={decodedSignature} copyText={decodedSignature} />
    </div>
  );
}
