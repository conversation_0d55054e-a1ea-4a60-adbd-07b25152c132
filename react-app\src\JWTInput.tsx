import React, { useRef, useEffect } from 'react';

interface JWTInputProps {
  encodedInput: string;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

function highlightParts(text: string): string {
  const parts = text.split('.');
  return parts
    .map((part, index) => {
      const color = index === 0 ? 'green' : index === 1 ? 'white' : 'blue';
      return `<span style="color:${color};">${part}</span>`;
    })
    .join('<span style="color:red;">.</span>');
}

export default function JWTInput({ encodedInput, onInputChange }: JWTInputProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isTyping = useRef(false);

  useEffect(() => {
    if (ref.current && !isTyping.current) {
      ref.current.innerHTML = highlightParts(encodedInput);
    }
  }, [encodedInput]);

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    isTyping.current = true;
    const text = e.currentTarget.textContent || '';
    const syntheticEvent = {
      target: { value: text },
      currentTarget: { value: text }
    } as React.ChangeEvent<HTMLTextAreaElement>;
    onInputChange(syntheticEvent);

    if (ref.current) {
      const selection = window.getSelection();
      const range = selection?.rangeCount ? selection.getRangeAt(0) : null;
      let cursorPosition = 0;

      if (range) {
        // Calculate cursor position in plain text
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(ref.current);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        cursorPosition = preCaretRange.toString().length;
      }

      // Update with highlighting
      ref.current.innerHTML = highlightParts(text);

      // Restore cursor position
      if (document.activeElement === ref.current && selection) {
        try {
          const newRange = document.createRange();
          const walker = document.createTreeWalker(
            ref.current,
            NodeFilter.SHOW_TEXT
          );

          let currentPos = 0;
          let node;
          while (node = walker.nextNode()) {
            const nodeLength = node.textContent?.length || 0;
            if (currentPos + nodeLength >= cursorPosition) {
              newRange.setStart(node, Math.min(cursorPosition - currentPos, nodeLength));
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);
              break;
            }
            currentPos += nodeLength;
          }
        } catch (e) {
          // Fallback: place cursor at end
          const newRange = document.createRange();
          newRange.selectNodeContents(ref.current);
          newRange.collapse(false);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
      }
    }
    isTyping.current = false;
  };
  
  
  return (
    <div className="input-container">
      <div className="input-section">
        <div className="input">
          <div
            ref={ref}
            contentEditable
            onInput={handleInput}
            className="jwt-input-editable"
            style={{
              minHeight: '100px',
              padding: '8px',
              outline: 'none',
              whiteSpace: 'pre-wrap',
            }}
          />
        </div>
      </div>
      
      <div className="signature-section">
        <div className="signature-header">
          <h4>Signature Verification</h4>
          <span className="optional-badge">(Optional)</span>
        </div>
        <div className="signature-input">
          <input
            type="password"
            placeholder="Enter secret key for verification..."
            className="secret-key-input"
          />
        </div>
        <div className="verification-status">
          <div className="status-indicator">
            <span className="status-icon">⚠</span>
            <span className="status-text">Enter secret key to verify</span>
          </div>
        </div>
      </div>
    </div>
  );
}
