import React, { useRef, useEffect } from 'react';

interface JWTInputProps {
  encodedInput: string;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

function highlightParts(text: string): string {
  const parts = text.split('.');
  return parts
    .map((part, index) => {
      const color = index === 0 ? 'red' : index === 1 ? 'green' : 'blue';
      return `<span style="color:${color};">${part}</span>`;
    })
    .join('<span style="color:red;">.</span>');
}

export default function JWTInput({ encodedInput, onInputChange }: JWTInputProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current && document.activeElement !== ref.current) {
      ref.current.innerHTML = highlightParts(encodedInput);
    }
  }, [encodedInput]);

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const text = e.currentTarget.textContent || '';
    const syntheticEvent = {
      target: { value: text },
      currentTarget: { value: text }
    } as React.ChangeEvent<HTMLTextAreaElement>;
    onInputChange(syntheticEvent);
  };
  
  
  return (
    <div className="input-container">
      <div className="input-section">
        <div className="input">
          <div
            ref={ref}
            contentEditable
            onInput={handleInput}
            className="jwt-input-editable"
            style={{
              minHeight: '100px',
              padding: '8px',
              outline: 'none',
              whiteSpace: 'pre-wrap',
            }}
          />
        </div>
      </div>
      
      <div className="signature-section">
        <div className="signature-header">
          <h4>Signature Verification</h4>
          <span className="optional-badge">(Optional)</span>
        </div>
        <div className="signature-input">
          <input
            type="password"
            placeholder="Enter secret key for verification..."
            className="secret-key-input"
          />
        </div>
        <div className="verification-status">
          <div className="status-indicator">
            <span className="status-icon">⚠</span>
            <span className="status-text">Enter secret key to verify</span>
          </div>
        </div>
      </div>
    </div>
  );
}
