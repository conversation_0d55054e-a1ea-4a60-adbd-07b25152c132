import React, { useRef, useEffect } from 'react';

interface JWTInputProps {
  encodedInput: string;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

function highlightParts(text: string): string {
  const parts = text.split('.');
  return parts
    .map((part, index) => {
      const className = index === 0 ? 'jwt-header' : index === 1 ? 'jwt-payload' : 'jwt-signature';
      return `<span class="${className}">${part}</span>`;
    })
    .join('<span class="jwt-separator">.</span>');
}

export default function JWTInput({ encodedInput, onInputChange }: JWTInputProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isTyping = useRef(false);

  useEffect(() => {
    if (ref.current && !isTyping.current) {
      ref.current.innerHTML = highlightParts(encodedInput);
    }
  }, [encodedInput]);

  const setCursorPosition = (element: HTMLElement, position: number) => {
    const selection = window.getSelection();
    if (!selection) return;

    const range = document.createRange();
    let charCount = 0;
    let nodeStack = [element];
    let node: Node | undefined;
    let foundStart = false;

    while (!foundStart && (node = nodeStack.pop())) {
      if (node.nodeType === Node.TEXT_NODE) {
        const nextCharCount = charCount + (node.textContent?.length || 0);
        if (position >= charCount && position <= nextCharCount) {
          range.setStart(node, position - charCount);
          foundStart = true;
        }
        charCount = nextCharCount;
      } else {
        for (let i = node.childNodes.length - 1; i >= 0; i--) {
          nodeStack.push(node.childNodes[i]);
        }
      }
    }

    if (foundStart) {
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  };

  const getCursorPosition = (element: HTMLElement): number => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return 0;

    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(element);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString().length;
  };

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    isTyping.current = true;
    const text = e.currentTarget.textContent || '';

    // Save cursor position before any changes
    const cursorPosition = getCursorPosition(e.currentTarget);

    const syntheticEvent = {
      target: { value: text },
      currentTarget: { value: text }
    } as React.ChangeEvent<HTMLTextAreaElement>;
    onInputChange(syntheticEvent);

    // Apply highlighting and restore cursor position after a brief delay
    setTimeout(() => {
      if (ref.current) {
        ref.current.innerHTML = highlightParts(text);

        // Restore cursor position
        if (document.activeElement === ref.current) {
          setCursorPosition(ref.current, cursorPosition);
        }
      }
      isTyping.current = false;
    }, 10); // Very short delay to avoid conflicts
  };
  
  
  return (
    <div className="input-container">
      <div className="input-section">
        <div className="input">
          <div
            ref={ref}
            contentEditable
            onInput={handleInput}
            className="jwt-input-editable"
            style={{
              minHeight: '100px',
              padding: '8px',
              outline: 'none',
              whiteSpace: 'pre-wrap',
            }}
          />
        </div>
      </div>
      
      <div className="signature-section">
        <div className="signature-header">
          <h4>Signature Verification</h4>
          <span className="optional-badge">(Optional)</span>
        </div>
        <div className="signature-input">
          <input
            type="password"
            placeholder="Enter secret key for verification..."
            className="secret-key-input"
          />
        </div>
        <div className="verification-status">
          <div className="status-indicator">
            <span className="status-icon">⚠</span>
            <span className="status-text">Enter secret key to verify</span>
          </div>
        </div>
      </div>
    </div>
  );
}
