html, body {
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #0f172a;
  color: #e2e8f0;
  font-family: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', 'Roboto Mono', monospace;
  overflow-y: auto; 
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  overflow-y: auto; 
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 10vh;
  background-color: #0f172a;
  color: #e2e8f0;
  border-bottom: 2px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 1.6rem;
  font-weight: 500;
  letter-spacing: 2.5px;
  margin: 0;
  text-transform: uppercase;
}

.block-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  min-height: 90vh;
  width: 90%;
  margin: auto;
  gap: 20px;
  padding-bottom: 20px; 
}

.block {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: #0f172a;
  margin: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}


.block-header {
  display: flex;
  align-items: center;
  height: 60px;
  width: 100%;
  margin: 5px 0 0;
  color: white;
  border-radius: 8px 8px 0 0;
  font-weight: 400;
  font-size: 1.8rem;
}

.block-body {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  gap: 12px;
  width: 100%;
  height: 100%;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.input-section {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input {
  flex: 1;
  border: 1px solid #334155;
  border-radius: 6px;
  overflow: hidden;
  width: 100%;
}

.signature-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 100px;
  width: 100%;
}

.signature-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 6px;
}

.signature-header h4 {
  margin: 0;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.optional-badge {
  color: #a1a1aa;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.secret-key-input {
  width: 100%;
  height: 40px;
  padding: 10px 12px;
  background-color: #0f172a;
  border: 1px solid #334155;
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 14px;
  font-family: monospace;
  outline: none;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.secret-key-input::placeholder {
  color: #64748b;
  font-style: italic;
}

.verification-status {
  padding: 8px 12px;
  background-color: #0f172a;
  border: 1px solid #334155;
  border-radius: 6px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  color: #94a3b8;
  font-size: 12px;
  font-weight: 500;
}

.block-body .button {
  display: flex;
  justify-content: flex-end;
  height: 60px;
}

button {
  background-color: #1e293b;
  border: 1px solid #334155;
  color: #e2e8f0;
  padding: 10px 14px;
  margin: 10px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 36px;
}

button:hover {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

button:active {
  background-color: #1e3a8a;
  transform: translateY(0);
}

button svg {
  width: 16px;
  height: 16px;
}

.jwt-input {
  width: 100%;
  height: 100%;
  font-size: 1.1rem;
  border: none;
  outline: none;
  padding: 13px;
  box-sizing: border-box;
  word-wrap: break-word;
  word-break: break-all;
  resize: none;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.jwt-input:empty:before {
  content: attr(data-placeholder);
  color: #64748b;
  font-style: italic;
  pointer-events: none;
}



