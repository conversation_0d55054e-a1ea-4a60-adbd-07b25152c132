import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"
import JWTInput from "./JWTInput.tsx"
import JWTOutput from "./JWTOutput.tsx"
import { decodeJWT } from "./jwtUtils"

function App() {
  const [encodedInput, setEncodedInput] = useState('');
  const [decodedHeader, setDecodedHeader] = useState('');
  const [decodedPayload, setDecodedPayload] = useState('');
  const [decodedSignature, setDecodedSignature] = useState('');

  // Function to decode JWT token
  const handleDecode = (input: string) => {
    const result = decodeJWT(input);
    setDecodedHeader(result.header);
    setDecodedPayload(result.payload);
    setDecodedSignature(result.signature);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setEncodedInput(value);
    handleDecode(value);
  };

  const encodedInputBody = (
    <JWTInput
      encodedInput={encodedInput}
      onInputChange={handleInputChange}
    />
  );

  const decodedOutputBody = (
    <JWTOutput
      decodedHeader={decodedHeader}
      decodedPayload={decodedPayload}
      decodedSignature={decodedSignature}
    />
  );

  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Encoded" body={encodedInputBody} />
      <Block header="Decoded" body={decodedOutputBody} />
    </div>
    </>
  );
}

export default App
