import React, { useState } from 'react';
import { verifySignature } from './jwtUtils';

interface SignatureVerificationProps {
  jwtToken: string;
}

export default function SignatureVerification({ jwtToken }: SignatureVerificationProps) {
  const [secretKey, setSecretKey] = useState('');
  const [verificationStatus, setVerificationStatus] = useState<{
    status: 'pending' | 'valid' | 'invalid' | 'error';
    message: string;
    icon: string;
  }>({
    status: 'pending',
    message: 'Enter secret key to verify',
    icon: '⚠'
  });

  const handleSecretKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSecretKey(e.target.value);
    
    // Reset to pending when user changes the key
    if (e.target.value.trim() === '') {
      setVerificationStatus({
        status: 'pending',
        message: 'Enter secret key to verify',
        icon: '⚠'
      });
    }
  };

  const verifySignatureOutput = async () => {
    if (!jwtToken || !secretKey.trim()) {
      setVerificationStatus({
        status: 'error',
        message: 'JWT token and secret key required',
        icon: '❌'
      });
      return;
    }

    try {
      const isValid = verifySignature(jwtToken, secretKey);
      
      if (isValid) {
        setVerificationStatus({
          status: 'valid',
          message: 'Signature verified successfully',
          icon: '✅'
        });
      } else {
        setVerificationStatus({
          status: 'invalid',
          message: 'Invalid signature',
          icon: '❌'
        });
      }
    } catch (error) {
      setVerificationStatus({
        status: 'error',
        message: 'Verification failed',
        icon: '❌'
      });
    }
  };


  return (
    <div className="signature-section">
      <div className="signature-header">
        <h4>Signature Verification</h4>
        <span className="optional-badge">(Optional)</span>
      </div>
      <div className="signature-input">
        <input
          type="password"
          placeholder="Enter secret key for verification..."
          className="secret-key-input"
          value={secretKey}
          onChange={handleSecretKeyChange}
          onKeyPress={(e) => e.key === 'Enter' && verifySignatureOutput()}
        />
      </div>
      <div className="verification-status">
        <div className="status-indicator">
          <span className="status-icon">{verificationStatus.icon}</span>
          <span className="status-text">{verificationStatus.message}</span>
        </div>
        {secretKey.trim() && (
          <button 
            onClick={verifySignature}
            className="verify-button"
            style={{ marginTop: '8px', fontSize: '12px', padding: '4px 8px' }}
          >
            Verify
          </button>
        )}
      </div>
    </div>
  );
}
