import * as jose from 'jose';

export interface JWTDecodeResult {
  header: string;
  payload: string;
  signature: string;
  isValid: boolean;
  error?: string;
}

export function decodeJWT(input: string): JWTDecodeResult {
  try {
    if (input.trim() === '') {
      return {
        header: '',
        payload: '',
        signature: '',
        isValid: false
      };
    }

    const parts = input.split('.');
    if (parts.length !== 3) {
      return {
        header: 'Invalid JWT format. JWT should have 3 parts separated by dots.',
        payload: '',
        signature: '',
        isValid: false,
        error: 'Invalid format'
      };
    }

    //decode header and payload
    const header = JSON.parse(atob(parts[0].replace(/-/g, '+').replace(/_/g, '/')));
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

    return {
      header: JSON.stringify(header, null, 2),
      payload: JSON.stringify(payload, null, 2),
      signature: parts[2],
      isValid: true
    };

  } catch (error) {
    return {
      header: 'Invalid JWT token or malformed JSON',
      payload: '',
      signature: '',
      isValid: false,
      error: 'Parse error'
    };
  }
}

export async function verifySignature(inputToken: string, secretKey: string): Promise<boolean> {
  try {
    //convert secret key to Uint8Array
    const secret = new TextEncoder().encode(secretKey);

    //verify the JWT using jose
    await jose.jwtVerify(inputToken, secret);
    return true;
  } catch (error) {
    return false;
  }
}
